import React, { useState } from "react";
import { useSelector } from "react-redux";
import { Grid, Button, Table, Input } from "semantic-ui-react";
import { Link } from "react-router-dom";
import { addDays, startOfDay } from "date-fns";
import _ from "lodash";
import ManagerTransactionsListItem from "./ManagerTransactionsListItem";
import { searchFilter } from "../../../app/common/util/util";


export default function ManagerTransactionsDashboard() {
    const [searchTerms, setSearchTerms] = useState("");
  
  const {
    // transActiveForManager,
    transUnderContractForManager,
    // transActiveListingForManager,
    // transActiveBuyerForManager,
    transClosedForManager,
  } = useSelector((state) => state.transaction);
    const filteredTransactions = transUnderContractForManager.filter(
      (transaction) =>
        transaction.closingDateTime &&
        transaction.closingDateTime >= startOfDay(new Date()) && transaction.closingDateTime < startOfDay(addDays(new Date(), 7))
    );
    const searchedTransactions = searchFilter(filteredTransactions, searchTerms);
  
  const sortedTransactions = _.orderBy(
    searchedTransactions,
    "closingDateTime",
    "asc"
  );

  let closedTransactionsByAgent = {};
  transClosedForManager.forEach((transaction) => {
    if (
      transaction.agentProfile?.firstName &&
      transaction.agentProfile?.lastName
    ) {
      let salesPrice = 0;
      if (transaction.salesPrice) {
        salesPrice = parseFloat(transaction.salesPrice.replace(/[$,]/g, ""));
      }
      const agentName = `${transaction.agentProfile?.firstName} ${transaction.agentProfile?.lastName}`;
      if (closedTransactionsByAgent[agentName]) {
        closedTransactionsByAgent[agentName].count += 1;
        closedTransactionsByAgent[agentName].totalSalesPrice += salesPrice;
      } else {
        closedTransactionsByAgent[agentName] = {
          count: 1,
          totalSalesPrice: salesPrice,
        };
      }
    }
  });

  

  return (
    <div className="main-page-wrapper">
      <>
        <Grid stackable className="large bottom margin"></Grid>
        <Grid>
          <Grid.Row>
            <Grid.Column
              computer={16}
              className="large top margin small bottom margin"
            >
              <h1
                className="zero bottom margin"
                style={{ position: "absolute", bottom: "0" }}
              >
                Manager Transactions
              </h1>
            </Grid.Column>
          </Grid.Row>
          <Grid.Row>
            <Grid.Column computer={5}>
              <Input
                type="text"
                fluid
                placeholder="Search"
                value={searchTerms}
                onChange={(e) => setSearchTerms(e.target.value)}
              ></Input>
            </Grid.Column>

            <Grid.Column computer={3} tablet={4}>
              <Button.Group fluid size="small">
                <Button active as={Link} to="/managerUpcomingClosings">
                  Upcoming Closings
                </Button>
                <Button as={Link} to={`/managerRecentClosings/`}>
                  Recent Closings
                </Button>
                <Button as={Link} to={`/managerNewListings/`}>
                  New Listings
                </Button>
                <Button as={Link} to={`/managerNewBuyers/`}>
                  New Buyers
                </Button>
                <Button as={Link} to={`/managerStorage/`} color="grey">
                  Storage
                </Button>
              </Button.Group>
            </Grid.Column>
          </Grid.Row>
          <Grid.Row>
            <Grid.Column
              computer={16}
              className="large top margin small bottom margin"
            >
              <h2
                className="zero bottom margin"
                style={{ position: "absolute", bottom: "0" }}
              >
                Upcoming Closings
              </h2>
            </Grid.Column>
          </Grid.Row>
          <Grid.Row>
            <Table compact>
              <Table.Header className="mobile hidden">
                <Table.Row className="small-header">
                  <Table.HeaderCell></Table.HeaderCell>
                  <Table.HeaderCell>Closing Date</Table.HeaderCell>
                  <Table.HeaderCell>Agent</Table.HeaderCell>
                  <Table.HeaderCell>
                    Client&nbsp;&nbsp;(Primary)
                  </Table.HeaderCell>
                  <Table.HeaderCell>
                    Client&nbsp;&nbsp;(Secondary)
                  </Table.HeaderCell>
                  <Table.HeaderCell>Representing</Table.HeaderCell>
                  <Table.HeaderCell>Address</Table.HeaderCell>
                </Table.Row>
              </Table.Header>
              <Table.Body>
                {sortedTransactions.map((transaction) => (
                  <ManagerTransactionsListItem
                    transaction={transaction}
                    key={transaction.id}
                    useDate={transaction.closingDateTime}
                  />
                ))}
              </Table.Body>
            </Table>
          </Grid.Row>
        </Grid>
      </>
    </div>
  );
}
