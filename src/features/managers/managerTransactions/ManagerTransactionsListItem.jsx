import React, { useState } from "react";
import { useNavigate } from "react-router-dom";
import { Table, Image, Icon, Button } from "semantic-ui-react";
import { format } from "date-fns";
import { convertAddressFull } from "../../../app/common/util/util";
import { storeTransaction } from "../../../app/firestore/firestoreService";
import { toast } from "react-toastify";

export default function ManagerTransactionsListItem({
  transaction,
  useDate,
  showLockIcon = false,
  onTransactionStored,
}) {
  const navigate = useNavigate();
  const [storing, setStoring] = useState(false);

  function handleRowClick() {
    navigate(`/transactions/${transaction.id}/overview`);
  }

  async function handleLockClick(e) {
    e.stopPropagation(); // Prevent row click

    if (transaction.isStored) {
      return; // Already stored, do nothing
    }

    try {
      setStoring(true);
      await storeTransaction(transaction);
      toast.success("Transaction stored successfully!");
      if (onTransactionStored) {
        onTransactionStored(transaction.id);
      }
    } catch (error) {
      console.error("Error storing transaction:", error);
      toast.error("Failed to store transaction");
    } finally {
      setStoring(false);
    }
  }

  return (
    <Table.Row
      key={transaction.id}
      onClick={() => handleRowClick()}
      style={{ cursor: "pointer" }}
    >
      <Table.Cell style={{ padding: "0px" }}>
        {transaction.pic ? (
          <Image style={{ width: "90px" }} src={transaction.pic} />
        ) : (
          <Image
            src="/assets/placeholder-house.png"
            style={{ width: "90px" }}
            rounded
          />
        )}
      </Table.Cell>
      <Table.Cell>
        {useDate && format(useDate, "MM/dd/yyyy")}
        {/* {transaction.closingDateTime &&
          format(transaction.closingDateTime, "MM/dd/yyyy")} */}
      </Table.Cell>
      <Table.Cell>
        {transaction.agentProfile?.firstName}{" "}
        {transaction.agentProfile?.lastName}
      </Table.Cell>
      <Table.Cell>
        {transaction.client?.firstName} {transaction.client?.lastName}
      </Table.Cell>
      <Table.Cell>
        {transaction.clientSecondary?.firstName}{" "}
        {transaction.clientSecondary?.lastName}
      </Table.Cell>
      <Table.Cell>{transaction.agentRepresents}</Table.Cell>
      <Table.Cell>{convertAddressFull(transaction.address)}</Table.Cell>
      {showLockIcon && (
        <Table.Cell textAlign="center">
          <Button
            icon
            size="mini"
            onClick={handleLockClick}
            loading={storing}
            disabled={storing}
            color={transaction.isStored ? "green" : "grey"}
            title={
              transaction.isStored
                ? "Transaction is stored"
                : "Store transaction"
            }
          >
            <Icon name={transaction.isStored ? "lock" : "unlock"} />
          </Button>
        </Table.Cell>
      )}
    </Table.Row>
  );
}
