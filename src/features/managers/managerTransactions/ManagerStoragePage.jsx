import React, { useState, useEffect } from "react";
import { Grid, Table, Button, Input, Icon, Message } from "semantic-ui-react";
import _ from "lodash";
import { useSelector, useDispatch } from "react-redux";
import { Link } from "react-router-dom";
import { format } from "date-fns";
import { convertAddressFull } from "../../../app/common/util/util";
import { getStoredTransactions, downloadStoredTransaction } from "../../../app/firestore/firestoreService";
import {
  fetchStoredTransactionsStart,
  fetchStoredTransactionsSuccess,
  fetchStoredTransactionsError
} from "../managerStorage/storageSlice";
import { toast } from "react-toastify";

export default function ManagerStoragePage() {
  const [searchTerms, setSearchTerms] = useState("");
  const [storedTransactions, setStoredTransactions] = useState([]);
  const [loading, setLoading] = useState(true);
  const [downloadingId, setDownloadingId] = useState(null);
  const { currentUserProfile } = useSelector((state) => state.profile);
  const dispatch = useDispatch();

  // Optional: Use Redux state (currently using local state for simplicity)
  // const { storedTransactions: reduxStoredTransactions, loading: reduxLoading } = useSelector((state) => state.storage);

  useEffect(() => {
    loadStoredTransactions();
  }, []);

  async function loadStoredTransactions() {
    try {
      setLoading(true);
      dispatch(fetchStoredTransactionsStart());
      const transactions = await getStoredTransactions(currentUserProfile.managerId || currentUserProfile.userId);
      setStoredTransactions(transactions);
      dispatch(fetchStoredTransactionsSuccess(transactions));
    } catch (error) {
      console.error("Error loading stored transactions:", error);
      dispatch(fetchStoredTransactionsError(error.message));
      toast.error("Failed to load stored transactions");
    } finally {
      setLoading(false);
    }
  }

  async function handleDownloadTransaction(storedTransaction) {
    try {
      setDownloadingId(storedTransaction.id);
      await downloadStoredTransaction(storedTransaction);
      toast.success("Transaction downloaded successfully!");
    } catch (error) {
      console.error("Error downloading transaction:", error);
      toast.error("Failed to download transaction");
    } finally {
      setDownloadingId(null);
    }
  }

  // Filter transactions based on search terms
  const filteredTransactions = storedTransactions.filter((transaction) => {
    if (!searchTerms) return true;
    const searchLower = searchTerms.toLowerCase();
    
    return (
      transaction.agentName?.toLowerCase().includes(searchLower) ||
      transaction.clientNames?.toLowerCase().includes(searchLower) ||
      transaction.address?.toLowerCase().includes(searchLower) ||
      format(transaction.dateStored.toDate(), "MM/dd/yyyy").includes(searchLower) ||
      (transaction.closingDate && format(transaction.closingDate.toDate(), "MM/dd/yyyy").includes(searchLower))
    );
  });

  const sortedTransactions = _.orderBy(filteredTransactions, "dateStored", "desc");

  return (
    <>
      <div className="main-content">
        <div className="main-content-inner">
          <Grid stackable>
            <Grid.Row>
              <Grid.Column computer={13} tablet={12}>
                <Input
                  icon="search"
                  placeholder="Search by agent, client, address, or date..."
                  value={searchTerms}
                  onChange={(e) => setSearchTerms(e.target.value)}
                  style={{ width: "100%" }}
                />
              </Grid.Column>

              <Grid.Column computer={3} tablet={4}>
                <Button.Group fluid size="small">
                  <Button as={Link} to="/managerUpcomingClosings">
                    Upcoming Closings
                  </Button>
                  <Button as={Link} to="/managerRecentClosings">
                    Recent Closings
                  </Button>
                  <Button as={Link} to="/managerNewListings">
                    New Listings
                  </Button>
                  <Button as={Link} to="/managerNewBuyers">
                    New Buyers
                  </Button>
                  <Button active color="grey">
                    Storage
                  </Button>
                </Button.Group>
              </Grid.Column>
            </Grid.Row>
            
            <Grid.Row>
              <Grid.Column
                computer={16}
                className="large top margin small bottom margin"
              >
                <h2
                  className="zero bottom margin"
                  style={{ position: "absolute", bottom: "0" }}
                >
                  Stored Transactions
                </h2>
              </Grid.Column>
            </Grid.Row>

            <Grid.Row>
              {loading ? (
                <Grid.Column width={16}>
                  <Message>Loading stored transactions...</Message>
                </Grid.Column>
              ) : (
                <Table compact>
                  <Table.Header className="mobile hidden">
                    <Table.Row className="small-header">
                      <Table.HeaderCell>Date Stored</Table.HeaderCell>
                      <Table.HeaderCell>Agent</Table.HeaderCell>
                      <Table.HeaderCell>Representing</Table.HeaderCell>
                      <Table.HeaderCell>Client Names</Table.HeaderCell>
                      <Table.HeaderCell>Address</Table.HeaderCell>
                      <Table.HeaderCell>Closing Date</Table.HeaderCell>
                      <Table.HeaderCell>Actions</Table.HeaderCell>
                    </Table.Row>
                  </Table.Header>
                  <Table.Body>
                    {sortedTransactions.length === 0 ? (
                      <Table.Row>
                        <Table.Cell colSpan="7" textAlign="center">
                          {searchTerms ? "No transactions found matching your search." : "No stored transactions found."}
                        </Table.Cell>
                      </Table.Row>
                    ) : (
                      sortedTransactions.map((transaction) => (
                        <Table.Row
                          key={transaction.id}
                          style={{ cursor: "pointer" }}
                          onClick={() => handleDownloadTransaction(transaction)}
                        >
                          <Table.Cell>
                            {format(transaction.dateStored.toDate(), "MM/dd/yyyy")}
                          </Table.Cell>
                          <Table.Cell>{transaction.agentName}</Table.Cell>
                          <Table.Cell>{transaction.representing}</Table.Cell>
                          <Table.Cell>{transaction.clientNames}</Table.Cell>
                          <Table.Cell>{transaction.address}</Table.Cell>
                          <Table.Cell>
                            {transaction.closingDate 
                              ? format(transaction.closingDate.toDate(), "MM/dd/yyyy")
                              : "N/A"
                            }
                          </Table.Cell>
                          <Table.Cell>
                            <Button
                              icon="download"
                              size="mini"
                              color="teal"
                              loading={downloadingId === transaction.id}
                              disabled={downloadingId === transaction.id}
                              onClick={(e) => {
                                e.stopPropagation();
                                handleDownloadTransaction(transaction);
                              }}
                            />
                          </Table.Cell>
                        </Table.Row>
                      ))
                    )}
                  </Table.Body>
                </Table>
              )}
            </Grid.Row>
          </Grid>
        </div>
      </div>
    </>
  );
}
