import { createSlice } from "@reduxjs/toolkit";

const initialState = {
  storedTransactions: [],
  loading: false,
  error: null,
};

const storageSlice = createSlice({
  name: "storage",
  initialState,
  reducers: {
    fetchStoredTransactionsStart(state) {
      state.loading = true;
      state.error = null;
    },
    fetchStoredTransactionsSuccess(state, action) {
      state.storedTransactions = action.payload;
      state.loading = false;
      state.error = null;
    },
    fetchStoredTransactionsError(state, action) {
      state.loading = false;
      state.error = action.payload;
    },
    addStoredTransaction(state, action) {
      state.storedTransactions.unshift(action.payload);
    },
    clearStoredTransactions(state) {
      state.storedTransactions = [];
    },
  },
});

export const {
  fetchStoredTransactionsStart,
  fetchStoredTransactionsSuccess,
  fetchStoredTransactionsError,
  addStoredTransaction,
  clearStoredTransactions,
} = storageSlice.actions;

export default storageSlice.reducer;
